<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Comment extends Model
{
    protected $fillable = [
        'user_id',
        'post_id',
        'content'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function post()
    {
        return $this->belongsTo(Post::class);
    }
    public function likedByUsers()
    {
        return $this->belongsToMany(User::class, 'comment_user_likes')->withTimestamps();
    }

    public function isLikedBy($user)
    {
        return $this->likedByUsers()->where('user_id', $user->id)->exists();
    }
}
