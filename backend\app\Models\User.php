<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'username',
        'nom',
        'prenom',
        'bio',
        'location',
        'website',
        'date_naissence',
        'sex',
        'email',
        'password',
        'profil',
        'cover',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function posts()
    {
        return $this->hasMany(Post::class);
    }

    public function savedPosts()
    {
        return $this->belongsToMany(Post::class, 'saved_posts')->withTimestamps();
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function likedPosts()
    {
        return $this->belongsToMany(Post::class, 'likes')->withTimestamps();
    }

    // Followers relationship - users who follow this user
    public function followers()
    {
        return $this->belongsToMany(User::class, 'followers', 'followed_id', 'follower_id')->withTimestamps();
    }

    // Following relationship - users this user follows
    public function following()
    {
        return $this->belongsToMany(User::class, 'followers', 'follower_id', 'followed_id')->withTimestamps();
    }

    // Get followers count
    public function getFollowersCountAttribute()
    {
        return $this->followers()->count();
    }

    // Get following count
    public function getFollowingCountAttribute()
    {
        return $this->following()->count();
    }

    // Get posts count
    public function getPostsCountAttribute()
    {
        return $this->posts()->count();
    }

    // Get profile image URL
    public function getProfilUrlAttribute()
    {
        if ($this->profil) {
            if (str_starts_with($this->profil, 'http')) {
                return $this->profil;
            }
            return asset('storage/' . $this->profil);
        }
        return asset('storage/profile_pic/default.jpg');
    }

    // Get cover image URL
    public function getCoverUrlAttribute()
    {
        if ($this->cover) {
            if (str_starts_with($this->cover, 'http')) {
                return $this->cover;
            }
            return asset('storage/' . $this->cover);
        }
        return asset('storage/cover_images/default.jpg');
    }
    public function likedComments()
    {
        return $this->belongsToMany(Comment::class, 'comment_user_likes')->withTimestamps();
    }
}

