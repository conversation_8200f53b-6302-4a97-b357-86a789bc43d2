import React, { useState } from 'react';
import Image from './image';
import api from '../services/api';

function Comment({ comment }) {
    return (
        <div className="comment flex gap-3 rounded-lg bg-gray-50 p-3 mb-2">
            <Image
                src={comment.user?.profil || '/default-avatar.jpg'}
                className='w-8 h-8 rounded-full object-cover'
                alt=""
                storagePrefix={true}
            />
            <div className="flex flex-col justify-between flex-1">
                <strong className='text-secondary text-sm'>
                    {comment.user?.username || `${comment.user?.nom} ${comment.user?.prenom}` || 'Unknown User'}
                </strong>
                <p className="text-gray-700 text-sm mt-1">{comment.content}</p>
                <span className="text-xs text-gray-400 mt-1">
                    {comment.created_at ? new Date(comment.created_at).toLocaleDateString() : 'Just now'}
                </span>
            </div>
        </div>
    );
}

function CommentSection({ comments, postId }) {
    const [newComment, setNewComment] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [commentsList, setCommentsList] = useState(comments || []);

    const handleSubmitComment = async (e) => {
        e.preventDefault();
        if (!newComment.trim() || isSubmitting) return;

        try {
            setIsSubmitting(true);
            const response = await api.post(`/posts/${postId}/comments`, {
                content: newComment
            });

            setCommentsList(prev => [response.data.comment, ...prev]);
            setNewComment('');
        } catch (err) {
            console.error('Error adding comment:', err);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <div className="comment-section my-4 border-t pt-4">
            <form onSubmit={handleSubmitComment} className="mb-4">
                <div className="flex gap-2">
                    <input
                        type="text"
                        value={newComment}
                        onChange={(e) => setNewComment(e.target.value)}
                        placeholder="Add a comment..."
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent text-sm"
                        disabled={isSubmitting}
                    />
                    <button
                        type="submit"
                        disabled={!newComment.trim() || isSubmitting}
                        className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                    >
                        {isSubmitting ? 'Posting...' : 'Post'}
                    </button>
                </div>
            </form>

            <div className="space-y-2">
                {commentsList.length > 0 ? (
                    commentsList.map((comment) => (
                        <Comment key={comment.id} comment={comment} />
                    ))
                ) : (
                    <p className="text-gray-500 text-sm text-center py-4">No comments yet. Be the first to comment!</p>
                )}
            </div>
        </div>
    );
}

export default CommentSection;
