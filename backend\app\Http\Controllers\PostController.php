<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Post;
use App\Models\Tag;
use App\Models\Image;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;
use Log;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        $posts = Post::with(['tags', 'images', 'user', 'comments.user', 'likes'])
            ->orderBy('created_at', 'desc')
            ->get();

        // Add computed fields for each post
        $posts->each(function ($post) use ($user) {
            $post->comments_count = $post->comments->count();
            $post->likes_count = $post->likes()->count();
            $post->is_liked = $user ? $post->likes()->where('user_id', $user->id)->exists() : false;
        });

        return response()->json($posts);
    }
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $tags = Tag::all();
        return response()->json($tags);
    }
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'description' => 'required',
            'location' => 'nullable|string',
            'tags' => 'array',
            'tags.*' => 'string',
            'images.*' => 'nullable|image'
        ]);

        // Debug authentication
        Log::info('Auth check: ' . (Auth::check() ? 'true' : 'false'));
        Log::info('Auth ID: ' . Auth::id());
        Log::info('Auth user: ', Auth::user() ? Auth::user()->toArray() : ['No user']);

        $post = new Post();
        $post->description = $request->description;
        $post->location = $request->location;

        // Try different methods to get user_id
        $post->user_id = Auth::id() ?? $request->user()->id ?? null;

        // Log the final user_id
        Log::info('Final user_id set: ' . $post->user_id);

        // Auto-generate map URL if location is provided
        if ($request->localisation) {
            $post->map_url = 'https://www.google.com/maps/search/?api=1&query=' . urlencode($request->localisation);
        }

        $post->save();

        // Créer les tags s'ils n'existent pas, puis les attacher
        if ($request->has('tags')) {
            $tagIds = [];

            foreach ($request->tags as $tagName) {
                $tag = Tag::firstOrCreate(['tag' => $tagName]);
                $tag->incrementUsage(); // Increment usage count
                $tagIds[] = $tag->id;
            }

            $post->tags()->sync($tagIds);
        }

        // Save multiple images
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $img) {
                $path = $img->store('posts/images', 'public');
                $post->images()->create(['image' => $path]);
            }
        }

        return response()->json(['message' => 'Post created successfully', 'post' => $post]);
    }


    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $post = Post::with(['tags', 'images', 'user'])->findOrFail($id);
        return response()->json($post);
    }
    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        $post = Post::with(['tags', 'images'])->findOrFail($id);
        $allTags = Tag::all();

        return response()->json([
            'post' => $post,
            'all_tags' => $allTags
        ]);
    }
    /**
     * Update the specified resource in storage.
     */
    public function Update(Request $request, $id)
    {
        $post = Post::findOrFail($id);
        $request->validate([
            'description' => 'sometimes|required|string',
            'localisation' => 'sometimes|nullable|string',
            'tags' => 'sometimes|array',
            'tags.*' => 'string',
            'images.*' => 'sometimes|image',
            'keep_media_ids' => 'sometimes|array',
            'keep_media_ids.*' => 'integer|exists:images,id'
        ]);
        if ($request->has('description')) {
            $post->description = $request->description;
        }
        if ($request->has('localisation')) {
            $post->localisation = $request->localisation;
        }
        $post->save();
        if ($request->has('tags')) {
            $tagIds = [];
            foreach ($request->tags as $tagName) {
                $tag = Tag::firstOrCreate(['tag' => $tagName]);
                $tag->incrementUsage(); // Increment usage count
                $tagIds[] = $tag->id;
            }
            $post->tags()->sync($tagIds);
        }
        if ($request->has('keep_media_ids')) {
            $keepIds = $request->keep_media_ids;
            $imagesToDelete = $post->images()->whereNotIn('id', $keepIds)->get();
            foreach ($imagesToDelete as $image) {
                // حذف من الـ storage (اختياري)
                if (\Storage::disk('public')->exists($image->path)) {
                    \Storage::disk('public')->delete($image->path);
                }
                $image->delete();
            }
        }
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $img) {
                $path = $img->store('posts/images', 'public');
                $post->images()->create(['path' => $path]);
            }
        }

        return response()->json([
            'message' => 'Post updated successfully',
            'post' => $post->load('tags', 'images')
        ]);
    }
    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $post = Post::findOrFail($id);
        // حذف الصور المرتبطة
        foreach ($post->images as $image) {
            $image->delete();
        }

        // حذف العلاقة مع التاغات
        $post->tags()->detach();

        $post->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }

    public function toggleLike(Request $request, $id)
    {
        $post = Post::findOrFail($id);
        $user = Auth::user();

        // Check if user already liked this post
        $existingLike = $post->likes()->where('user_id', $user->id)->first();

        if ($existingLike) {
            // Unlike the post
            $existingLike->delete();
            $post->decrement('likes');
            $liked = false;
            $message = 'Post unliked successfully';
        } else {
            // Like the post
            $post->likes()->create(['user_id' => $user->id]);
            $post->increment('likes');
            $liked = true;
            $message = 'Post liked successfully';
        }

        return response()->json([
            'message' => $message,
            'liked' => $liked,
            'likes_count' => $post->fresh()->likes
        ]);
    }

    public function incrementView(Request $request, $id)
    {
        $post = Post::findOrFail($id);
        $post->incrementViews();

        return response()->json([
            'message' => 'View recorded',
            'views' => $post->views
        ]);
    }

    public function addComment(Request $request, $id)
    {
        $request->validate([
            'content' => 'required|string|max:1000'
        ]);

        $post = Post::findOrFail($id);
        $user = Auth::user();

        $comment = $post->comments()->create([
            'user_id' => $user->id,
            'content' => $request->content
        ]);

        $comment->load('user');

        return response()->json([
            'message' => 'Comment added successfully',
            'comment' => $comment
        ]);
    }

    public function getComments(Request $request, $id)
    {
        $post = Post::findOrFail($id);
        $comments = $post->comments()->with('user')->orderBy('created_at', 'desc')->get();

        return response()->json($comments);
    }
    public function like(Comment $comment)
    {
        $user = auth()->user();

        if ($comment->isLikedBy($user)) {
            $comment->likedByUsers()->detach($user->id); // unlike
        } else {
            $comment->likedByUsers()->attach($user->id); // like
        }
        return back();
    }

}









